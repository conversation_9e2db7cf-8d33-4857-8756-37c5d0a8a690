import * as Location from 'expo-location';
import { store, api } from 'common';
import { calculateDistance, isValidLocation } from '../utils/LocationUtils';
import apiMonitor from '../utils/ApiMonitor';

// Optimal settings for smooth tracking
const TRACKING_CONFIG = {
  GPS_UPDATE_INTERVAL: 3000,      // 3 seconds
  GPS_DISTANCE_THRESHOLD: 5,      // 5 meters
  ANIMATION_DURATION: 800,        // not used here, reference for UI
  DEBOUNCE_DELAY: 1000,           // not used here, reference for UI
  MIN_ACCURACY: 50,               // 50 meters
  KALMAN_PROCESS_NOISE: 0.00001,
  KALMAN_MEASUREMENT_NOISE: 0.01,
};

// Lightweight Kalman filter for lat/lng smoothing
class Kalman1D {
  constructor(processNoise, measurementNoise) {
    this.processNoise = processNoise;
    this.measurementNoise = measurementNoise;
    this.estimated = null;
    this.errorCovariance = 1;
  }
  filter(measurement) {
    if (this.estimated === null) {
      this.estimated = measurement;
      return measurement;
    }
    // Predict
    this.errorCovariance = this.errorCovariance + this.processNoise;
    // Update
    const kalmanGain = this.errorCovariance / (this.errorCovariance + this.measurementNoise);
    this.estimated = this.estimated + kalmanGain * (measurement - this.estimated);
    this.errorCovariance = (1 - kalmanGain) * this.errorCovariance;
    return this.estimated;
  }
}

class SimpleLocationTracker {
  constructor() {
    this.watchId = null;
    this.isTracking = false;
    this.lastLocation = null;
    this.routeCache = new Map();
    this.lastRouteFetch = 0;
    this.routeFetchInterval = 2 * 60 * 1000; // 2 minutes between route fetches (BEST CASE)
    this.minDistanceForNewRoute = 2000; // 2km minimum for new route (BEST CASE)
    this.apiCallCount = 0; // Track API calls for monitoring
    this.currentBookingId = null; // Track current booking for API storage
    this.bookingApiCounts = new Map(); // Store API counts per booking
    this.routePredictionCache = new Map(); // Cache for route predictions
    this.lastSignificantLocation = null; // Track last significant location change
    // New: history & smoothing
    this.locationHistory = [];
    this.maxHistorySize = 10;
    this.latFilter = new Kalman1D(TRACKING_CONFIG.KALMAN_PROCESS_NOISE, TRACKING_CONFIG.KALMAN_MEASUREMENT_NOISE);
    this.lngFilter = new Kalman1D(TRACKING_CONFIG.KALMAN_PROCESS_NOISE, TRACKING_CONFIG.KALMAN_MEASUREMENT_NOISE);
    this.speedBand = 'slow'; // slow | medium | fast
  }

  setKalmanTuningForSpeed(speedMs) {
    let band = 'slow';
    if (speedMs > 15) band = 'fast';
    else if (speedMs > 5) band = 'medium';
    if (band === this.speedBand) return;
    this.speedBand = band;
    // Higher process noise -> quicker response; higher measurement noise -> less weight to noisy inputs
    if (band === 'fast') {
      this.latFilter = new Kalman1D(0.001, 0.05);
      this.lngFilter = new Kalman1D(0.001, 0.05);
      this.maxHistorySize = 4;
    } else if (band === 'medium') {
      this.latFilter = new Kalman1D(0.0001, 0.02);
      this.lngFilter = new Kalman1D(0.0001, 0.02);
      this.maxHistorySize = 6;
    } else {
      this.latFilter = new Kalman1D(TRACKING_CONFIG.KALMAN_PROCESS_NOISE, TRACKING_CONFIG.KALMAN_MEASUREMENT_NOISE);
      this.lngFilter = new Kalman1D(TRACKING_CONFIG.KALMAN_PROCESS_NOISE, TRACKING_CONFIG.KALMAN_MEASUREMENT_NOISE);
      this.maxHistorySize = 10;
    }
    // Reset history when tuning changes to avoid drift
    this.locationHistory = [];
  }

  async startTracking(onLocationUpdate, onError, bookingId = null) {
    try {
      // Set current booking ID for API tracking
      this.currentBookingId = bookingId;
      
      // Initialize API count for this booking
      if (bookingId && !this.bookingApiCounts.has(bookingId)) {
        this.bookingApiCounts.set(bookingId, {
          totalCalls: 0,
          startTime: Date.now(),
          lastCallTime: null
        });
      }

      // Check permissions
      const { status } = await Location.getForegroundPermissionsAsync();
      if (status !== 'granted') {
        const { status: newStatus } = await Location.requestForegroundPermissionsAsync();
        if (newStatus !== 'granted') {
          throw new Error('Location permission denied');
        }
      }

      // Clear existing watcher
      if (this.watchId) {
        await Location.clearWatchAsync(this.watchId);
      }

      // Start watching with optimized settings for live tracking
      this.watchId = await Location.watchPositionAsync(
        {
          accuracy: Location.Accuracy.Balanced,
          timeInterval: TRACKING_CONFIG.GPS_UPDATE_INTERVAL,
          distanceInterval: TRACKING_CONFIG.GPS_DISTANCE_THRESHOLD,
          mayShowUserSettingsDialog: false,
        },
        (location) => {
          const raw = {
            lat: location.coords.latitude,
            lng: location.coords.longitude,
            timestamp: Date.now(),
            accuracy: location.coords.accuracy,
            speed: location.coords.speed || 0,
          };

          // Basic validity and accuracy filter
          if (!isValidLocation(raw) || (raw.accuracy && raw.accuracy > TRACKING_CONFIG.MIN_ACCURACY)) {
            return;
          }

          // Smooth with Kalman filters
          // Tune smoothing dynamically by speed
          this.setKalmanTuningForSpeed(raw.speed || 0);

          const smoothedLat = this.latFilter.filter(raw.lat);
          const smoothedLng = this.lngFilter.filter(raw.lng);
          const smoothed = { ...raw, lat: smoothedLat, lng: smoothedLng };

          // Append to history and trim
          this.locationHistory.push(smoothed);
          if (this.locationHistory.length > this.maxHistorySize) {
            this.locationHistory.shift();
          }

          // Simple averaging of last N points to further smooth
          const windowSize = Math.min(this.speedBand === 'fast' ? 3 : this.speedBand === 'medium' ? 4 : 5, this.locationHistory.length);
          const recent = this.locationHistory.slice(-windowSize);
          const avgLat = recent.reduce((s, p) => s + p.lat, 0) / windowSize;
          const avgLng = recent.reduce((s, p) => s + p.lng, 0) / windowSize;
          const averaged = { ...smoothed, lat: avgLat, lng: avgLng };

          // Filter out tiny movements to avoid jitter
          if (this.lastLocation) {
            const movedMeters = calculateDistance(
              averaged.lat,
              averaged.lng,
              this.lastLocation.lat,
              this.lastLocation.lng
            ) * 1000;
            if (movedMeters < TRACKING_CONFIG.GPS_DISTANCE_THRESHOLD) {
              return;
            }
          }

          this.lastLocation = averaged;

          // Update Redux store
          store.dispatch({
            type: 'UPDATE_GPS_LOCATION',
            payload: averaged,
          });

          // Call callback
          if (onLocationUpdate) {
            onLocationUpdate(averaged);
          }
        },
        (error) => {
          console.error('Location tracking error:', error);
          if (onError) {
            onError(error);
          }
        }
      );

      this.isTracking = true;
      return true;
    } catch (error) {
      console.error('Failed to start location tracking:', error);
      if (onError) {
        onError(error);
      }
      return false;
    }
  }

  async stopTracking() {
    if (this.watchId) {
      await Location.clearWatchAsync(this.watchId);
      this.watchId = null;
    }
    this.isTracking = false;
  }

  // Get route with 2-minute API throttling and smart caching (BEST CASE)
  async getCachedRoute(startLoc, destLoc, waypoints = '') {
    const routeKey = `${startLoc.lat},${startLoc.lng}_${destLoc.lat},${destLoc.lng}`;
    const now = Date.now();

    // Check if enough time has passed since last API call (2 minutes)
    const timeSinceLastCall = now - this.lastRouteFetch;
    if (timeSinceLastCall < this.routeFetchInterval) {
      // Return cached route if available
      if (this.routeCache.has(routeKey)) {
        console.log(`📋 Using cached route (${Math.round(timeSinceLastCall/1000)}s since last API call)`);
        return this.routeCache.get(routeKey).data;
      }
      // If no cache and too soon, return null to avoid API call
      console.log(`⏰ API call throttled (${Math.round(timeSinceLastCall/1000)}s < 120s)`);
      return null;
    }

    // Check if we need a new route based on distance (2km threshold)
    let shouldFetch = true;
    if (this.lastSignificantLocation && this.lastRouteFetch > 0) {
      const distanceMoved = calculateDistance(
        startLoc.lat, startLoc.lng,
        this.lastSignificantLocation.lat, this.lastSignificantLocation.lng
      ) * 1000; // Convert to meters

      shouldFetch = distanceMoved >= this.minDistanceForNewRoute;
      
      if (!shouldFetch) {
        console.log(`📍 Not enough movement (${Math.round(distanceMoved)}m < 2000m)`);
      }
    }

    if (!shouldFetch && this.routeCache.has(routeKey)) {
      console.log(`📋 Using cached route (insufficient movement)`);
      return this.routeCache.get(routeKey).data;
    }

    // Check for route prediction cache
    const predictionKey = `prediction_${destLoc.lat},${destLoc.lng}`;
    if (this.routePredictionCache.has(predictionKey)) {
      const prediction = this.routePredictionCache.get(predictionKey);
      const timeSincePrediction = now - prediction.timestamp;
      
      // Use prediction if it's less than 5 minutes old
      if (timeSincePrediction < 5 * 60 * 1000) {
        console.log(`🔮 Using route prediction (${Math.round(timeSincePrediction/1000)}s old)`);
        return prediction.data;
      }
    }

    try {
      // Make API call only every 2 minutes
      console.log(`🚀 Making API call #${++this.apiCallCount} at ${new Date().toISOString()}`);
      
      const routeData = await api.getDirectionsApi(
        `${startLoc.lat},${startLoc.lng}`,
        `${destLoc.lat},${destLoc.lng}`,
        waypoints
      );

      // Log successful API call
      apiMonitor.logApiCall('Directions', true);

      // Update booking API count
      if (this.currentBookingId) {
        const bookingStats = this.bookingApiCounts.get(this.currentBookingId);
        if (bookingStats) {
          bookingStats.totalCalls++;
          bookingStats.lastCallTime = now;
        }
      }

      // Cache the route with extended TTL
      this.routeCache.set(routeKey, {
        data: routeData,
        timestamp: now,
        ttl: 10 * 60 * 1000 // 10 minutes TTL
      });

      // Cache route prediction for destination
      this.routePredictionCache.set(predictionKey, {
        data: routeData,
        timestamp: now
      });

      // Update significant location
      this.lastSignificantLocation = { lat: startLoc.lat, lng: startLoc.lng };
      this.lastRouteFetch = now;

      // Clean old cache entries (keep only last 5 routes)
      if (this.routeCache.size > 5) {
        const entries = Array.from(this.routeCache.entries());
        entries.sort((a, b) => b[1].timestamp - a[1].timestamp);
        this.routeCache.clear();
        entries.slice(0, 5).forEach(([key, value]) => {
          this.routeCache.set(key, value);
        });
      }

      return routeData;
    } catch (error) {
      console.error('Failed to fetch route:', error);
      // Log failed API call
      apiMonitor.logApiCall('Directions', false);
      // Return cached route if available
      return this.routeCache.get(routeKey)?.data;
    }
  }

  getCurrentLocation() {
    return this.lastLocation;
  }

  isActive() {
    return this.isTracking;
  }

  clearCache() {
    this.routeCache.clear();
    this.routePredictionCache.clear();
    this.lastSignificantLocation = null;
  }

  // Clear prediction cache only
  clearPredictionCache() {
    this.routePredictionCache.clear();
  }

  // Get API call statistics for monitoring
  getApiCallStats() {
    const monitorStats = apiMonitor.getStats();
    const costs = apiMonitor.getCostEstimate();
    
    return {
      totalCalls: this.apiCallCount,
      lastCallTime: this.lastRouteFetch,
      cacheSize: this.routeCache.size,
      isTracking: this.isTracking,
      monitorStats,
      costs
    };
  }

  // Reset API call counter
  resetApiCallCount() {
    this.apiCallCount = 0;
  }

  // Get API count for specific booking
  getBookingApiCount(bookingId) {
    return this.bookingApiCounts.get(bookingId) || {
      totalCalls: 0,
      startTime: null,
      lastCallTime: null
    };
  }

  // Get all booking API counts
  getAllBookingApiCounts() {
    return Array.from(this.bookingApiCounts.entries()).map(([bookingId, stats]) => ({
      bookingId,
      ...stats,
      duration: stats.startTime ? Date.now() - stats.startTime : 0,
      cost: stats.totalCalls * 0.005 // $0.005 per API call
    }));
  }

  // Store API count in booking data
  async storeApiCountInBooking(bookingId) {
    try {
      const bookingStats = this.getBookingApiCount(bookingId);
      const apiData = {
        totalApiCalls: bookingStats.totalCalls,
        apiCost: bookingStats.totalCalls * 0.005,
        trackingStartTime: bookingStats.startTime,
        lastApiCallTime: bookingStats.lastCallTime,
        duration: bookingStats.startTime ? Date.now() - bookingStats.startTime : 0
      };

      // Try direct Firebase update first
      try {
        const { firebase } = require('common');
        const { update } = require('firebase/database');
        
        await update(firebase.singleBookingRef(bookingId), {
          apiUsageData: apiData
        });

        console.log(`📊 Stored API data for booking ${bookingId}:`, apiData);
        return apiData;
      } catch (firebaseError) {
        console.warn('Direct Firebase update failed, trying API method:', firebaseError);
        
        // Fallback to API method
        const { api } = require('common');
        await api.updateBooking({
          id: bookingId,
          apiUsageData: apiData
        });

        console.log(`📊 Stored API data for booking ${bookingId} (via API):`, apiData);
        return apiData;
      }
    } catch (error) {
      console.error('Failed to store API count in booking:', error);
      return null;
    }
  }

  // End tracking for a booking and store final API count
  async endBookingTracking(bookingId) {
    try {
      const apiData = await this.storeApiCountInBooking(bookingId);
      
      // Remove from active tracking
      if (this.currentBookingId === bookingId) {
        this.currentBookingId = null;
      }
      
      return apiData;
    } catch (error) {
      console.error('Failed to end booking tracking:', error);
      return null;
    }
  }
}

const simpleLocationTracker = new SimpleLocationTracker();
export default simpleLocationTracker;

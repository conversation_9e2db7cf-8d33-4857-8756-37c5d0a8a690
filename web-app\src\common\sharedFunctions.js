import {
  FormControl<PERSON><PERSON>l,
  <PERSON>rid,
  Switch, TextField, Typography
} from '@mui/material';
import InfoIcon from '@mui/icons-material/Info';
import OtherPerson from 'components/OtherPerson';
import moment from 'moment';
import { useTranslation } from "react-i18next";
import { colors } from "../components/Theme/WebTheme";
export const calcEst = false;
export const showEst = true;
export const optionsRequired = false;


export const MAIN_COLOR = colors.TAXIPRIMARY;
export const SECONDORY_COLOR = colors.TAXISECONDORY;

export const FONT_FAMILY = '"Roboto", "Helvetica", "Arial", sans-serif';

export const bookingHistoryColumns = (role, settings, t, isRTL, driverNameById) => [
  { title: t('booking_ref'), field: 'reference'},
  { title: t('trip_start_date'), field: 'trip_start_date', render: rowData => rowData.tripdate?moment(rowData.tripdate).format('MMM D, YYYY h:mm A'):null},
  { title: t('car_type'), field: 'carType'},
  { 
    title: t('AdminAssigned'), 
    field: 'driver_name',
    render: rowData => {
      const req = rowData?.requestedDrivers ? Object.keys(rowData.requestedDrivers) : [];
      if (!req.length) {
        const name = rowData?.driver_name ? rowData.driver_name.trim().split(/\s+/)[0] : '';
        return <span>{name}</span>;
      }
      return (
        <div style={{ display: 'flex', flexWrap: 'wrap', gap: 8, justifyContent: 'center' }}>
          {req.map(id => {
            const isAccepted = rowData?.driver && id === rowData.driver;
            const first = driverNameById?.[id] 
              || (isAccepted && rowData?.driver_name ? rowData.driver_name.trim().split(/\s+/)[0] : (id || '').slice(0,6));
            return (
              <span 
                key={id} 
                style={{ color: isAccepted ? colors.GREEN : colors.BLACK, fontWeight: isAccepted ? 'bold' : 'normal' }}
              >
                {first},
              </span>
            );
          })}
        </div>
      );
    }
  },
  { title: t('DriverAccepted'), field: 'driver_name'},
  // { title: t('booking_status_web'), field: 'status', ... },
  { title: t("Trip Type"),field: "roundTrip",
    render: rowData => { 
      let displayTrip;
      displayTrip = rowData.roundTrip  === "true" ? "Round Trip" : "One Way Trip";
      return (
        <div style={{ padding: 7, margin: 'auto' }}>
          {t(displayTrip)}
        </div>
      );
    } }, 
  { title: t('drop_address'), field: 'dropAddress',
    render: rowData => {
      let backgroundColor;
      let displayAddress;

      if (rowData.dropAddress.includes("O'Hare International Airport")) {
        backgroundColor = colors.GREEN;
        displayAddress = "ORD";
      } else if (rowData.dropAddress.includes("Midway International Airport")) {
        backgroundColor = "#6C63FF";
        displayAddress = "MDW";
      } else {
        backgroundColor = colors.RED;
        displayAddress = rowData.dropAddress;
      }

      return (
        <div
          style={{
            backgroundColor: backgroundColor,
            color: "white",
            padding: 7,
            borderRadius: "15px",
            fontWeight: "bold",
            width: "150px",
            margin: 'auto'
          }}
        >
          {t(displayAddress)}
        </div>
      );
    }
  },
  {
    title: t('trip_cost'), field: 'dropAddress',
    render: (rowData) =>
      rowData.trip_cost
        ? settings.swipe_symbol
          ? rowData.trip_cost + " " + settings.symbol
          : settings.symbol + " " + rowData.trip_cost
        : settings.swipe_symbol
          ? "0 " + settings.symbol
          : settings.symbol + " 0",
  },
];

export const Todayorders = (role, settings, t, isRTL, driverNameById) => [
  { title: t('booking_ref'), field: 'reference'},
  { title: t('trip_start_date'), field: 'trip_start_date', 
    render: rowData => rowData.tripdate?moment(rowData.tripdate).format('MMM D, YYYY h:mm A'):null
  },
  { title: t('car_type'), field: 'carType'},
  {
    title: t('AdminAssigned'),
    field: 'driver_name',
    render: rowData => {
      const ids = rowData?.requestedDrivers ? Object.keys(rowData.requestedDrivers) : [];
      if (!ids.length) {
        const name = rowData?.driver_name ? rowData.driver_name.trim().split(/\s+/)[0] : '';
        return <span>{name}</span>;
      }
      return (
        <div style={{ display: 'flex', flexWrap: 'wrap', gap: 8, justifyContent: 'center' }}>
          {ids.map(id => {
            const isAccepted = rowData?.driver && id === rowData.driver;
            const first = driverNameById?.[id]
              || (isAccepted && rowData?.driver_name ? rowData.driver_name.trim().split(/\s+/)[0] : (id || '').slice(0,6));
            return (
              <span
                key={id}
                style={{ color: isAccepted ? colors.GREEN : colors.BLACK, fontWeight: isAccepted ? 'bold' : 'normal' }}
              >
                {first},
              </span>
            );
          })}
        </div>
      );
    }
  },
  { title: t('DriverAccepted'), field: 'driver_name'},
  { title: t("Trip Type"),field: "roundTrip",
    render: rowData => { 
      let displayTrip;
      displayTrip = rowData.roundTrip  === "true" ? "Round Trip" : "One Way Trip";
      return (
        <div
          style={{  
            padding: 7,  
            margin: 'auto'
          }}
        >
          {t(displayTrip)}
        </div>
      );
    } }, 
  { title: t('booking_status_web'), field: 'status',
   render: rowData => 
  <div
  style={{backgroundColor:rowData.status === "CANCELLED"?colors.RED :rowData.status=== "COMPLETE"?colors.GREEN : colors.YELLOW, color:"white", padding:7, borderRadius:"15px", fontWeight:"bold", width:"150px", margin: 'auto' }}
  >{t(rowData.status)}</div>,  },
    { title: t('otp'), field: 'otp',
  },
  {
    title: t('trip_cost'), field: 'trip_cost',
    render: (rowData) =>
      rowData.trip_cost
        ? settings.swipe_symbol
          ? rowData.trip_cost + " " + settings.symbol
          : settings.symbol + " " + rowData.trip_cost
        : settings.swipe_symbol
          ? "0 " + settings.symbol
          : settings.symbol + " 0",
  },
];

export const Neworders = (role, settings, t, isRTL, driverNameById) => [
  { title: t('booking_ref'), field: 'reference'},
  { title: t('trip_start_date'), field: 'trip_start_date', 
    render: rowData => (
      <div style={{ width: 150 }}>
        {rowData.tripdate ? moment(rowData.tripdate).format('MMM D, YYYY h:mm A') : null}
      </div>
    )
  },
  { title: t('car_type'), field: 'carType',
    render: rowData => {
      let bgColor = undefined;
      if (rowData.carType && typeof rowData.carType === 'string') {
        const carTypeValue = rowData.carType.trim().toLowerCase();
        if (carTypeValue === 'minivan') {
          bgColor = '#FF6B6B'; // bright red
        } else if (carTypeValue === 'sedan') {
          bgColor = '#4FC3F7'; // bright blue
        }
      }
      return (
        <div style={{ backgroundColor: bgColor, padding: 7, borderRadius: '8px', margin: 'auto', fontWeight: 'bold', color: bgColor ? 'white' : undefined }}>
          {rowData.carType}
        </div>
      );
    }
  },
  {
    title: t('AdminAssigned'),
    field: 'driver_name',
    render: rowData => {
      const ids = rowData?.requestedDrivers ? Object.keys(rowData.requestedDrivers) : [];
      if (!ids.length) {
        const name = rowData?.driver_name ? rowData.driver_name.trim().split(/\s+/)[0] : '';
        return <span>{name}</span>;
      }
      return (
        <div style={{ display: 'flex', flexWrap: 'wrap', gap: 8, justifyContent: 'center' }}>
          {ids.map(id => {
            const isAccepted = rowData?.driver && id === rowData.driver;
            const first = driverNameById?.[id]
              || (isAccepted && rowData?.driver_name ? rowData.driver_name.trim().split(/\s+/)[0] : (id || '').slice(0,6));
            return (
              <span
                key={id}
                style={{ color: isAccepted ? colors.GREEN : colors.BLACK, fontWeight: isAccepted ? 'bold' : 'normal' }}
              >
                {first},
              </span>
            );
          })}
        </div>
      );
    }
  },
  // { title: t('DriverAccepted'), field: 'driver_name'},
  { title: t('Trip From'), field: 'pickupAddress',
    render: rowData => (
      <div style={{ width: 150 }}>
        {rowData.pickupAddress}
      </div>
    )
  },
  { title: t('Trip To'), field: 'dropAddress',
    render: rowData => (
      <div style={{ width: 150 }}>
        {rowData.dropAddress}
      </div>
    )
  },
  { title: t("Trip Type"),field: "roundTrip",
    render: rowData => { 
      let displayTrip;
      
      displayTrip = rowData.roundTrip  === "true"  ? "Round Trip" : "One Way Trip";
      return (
        <div style={{ width: 150 }}>
          {t(displayTrip)}
        </div>
      );
    } }, 
  { title: t('booking_status_web'), field: 'status',
   render: rowData => 
    
  <div
  style={{backgroundColor:
    rowData.status === "CANCELLED" ? colors.RED :
    rowData.status === "COMPLETE" ? colors.GREEN :
    rowData.status === "NEW" ? "#347433" :
    colors.YELLOW,
    color:"white", padding:7, borderRadius:"15px", fontWeight:"bold", width:"150px", margin: 'auto' }}
  >{t(rowData.status)}</div>,  },
  // { title: t('otp'), field: 'otp',
  // },
  {
    title: t('trip_cost'), field: 'trip_cost',
    render: (rowData) =>
      rowData.trip_cost
        ? settings.swipe_symbol
          ? rowData.trip_cost + " " + settings.symbol
          : settings.symbol + " " + rowData.trip_cost
        : settings.swipe_symbol
          ? "0 " + settings.symbol
          : settings.symbol + " 0",
  },
];

export const Complete_trips = (role, settings, t, isRTL, driverNameById) => [
  { title: t('booking_ref'), field: 'reference'},
  { title: t('trip_start_date'), field: 'trip_start_date', 
    render: rowData => rowData.tripdate?moment(rowData.tripdate).format('MMM D, YYYY h:mm A'):null
  },
  { title: t('car_type'), field: 'carType'},
  {
    title: t('AdminAssigned'),
    field: 'driver_name',
    render: rowData => {
      const ids = rowData?.requestedDrivers ? Object.keys(rowData.requestedDrivers) : [];
      if (!ids.length) {
        const name = rowData?.driver_name ? rowData.driver_name.trim().split(/\s+/)[0] : '';
        return <span>{name}</span>;
      }
      return (
        <div style={{ display: 'flex', flexWrap: 'wrap', gap: 8, justifyContent: 'center' }}>
          {ids.map(id => {
            const isAccepted = rowData?.driver && id === rowData.driver;
            const first = driverNameById?.[id]
              || (isAccepted && rowData?.driver_name ? rowData.driver_name.trim().split(/\s+/)[0] : (id || '').slice(0,6));
            return (
              <span
                key={id}
                style={{ color: isAccepted ? colors.GREEN : colors.BLACK, fontWeight: isAccepted ? 'bold' : 'normal' }}
              >
                {first},
              </span>
            );
          })}
        </div>
      );
    }
  },
  { title: t('DriverAccepted'), field: 'driver_name'},
    { title: t("Trip Type"),field: "roundTrip",
    render: rowData => { 
      let displayTrip;
      
      displayTrip = rowData.roundTrip  === "true" ? "Round Trip" : "One Way Trip";
      return (
        <div
          style={{  
            padding: 7,  
            margin: 'auto'
          }}
        >
          {t(displayTrip)}
        </div>
      );
    } }, 
  { title: t('booking_status_web'), field: 'status',
   render: rowData => 
  <div
  style={{backgroundColor:rowData.status === "CANCELLED"?colors.RED :rowData.status=== "COMPLETE"?colors.GREEN : colors.YELLOW, color:"white", padding:7, borderRadius:"15px", fontWeight:"bold", width:"150px", margin: 'auto' }}
  >{t(rowData.status)}</div>,  },
    { title: t('otp'), field: 'otp',
  },
  {
    title: t('trip_cost'), field: 'trip_cost',
    render: (rowData) =>
      rowData.trip_cost
        ? settings.swipe_symbol
          ? rowData.trip_cost + " " + settings.symbol
          : settings.symbol + " " + rowData.trip_cost
        : settings.swipe_symbol
          ? "0 " + settings.symbol
          : settings.symbol + " 0",
  },
];
export const ManagebookingColumns = (role, settings, t, isRTL, driverNameById) => [
  { title: t('booking_ref'), field: 'reference'},
  { title: t('Trip date'), field: 'tripdate', render: rowData => rowData.tripdate?moment(rowData.tripdate).format('MMM D, YYYY h:mm A'):null},
  { title: t('car_type'), field: 'carType'},
  {
    title: t('AdminAssigned'),
    field: 'driver_name',
    render: rowData => {
      const ids = rowData?.requestedDrivers ? Object.keys(rowData.requestedDrivers) : [];
      if (!ids.length) {
        const name = rowData?.driver_name ? rowData.driver_name.trim().split(/\s+/)[0] : '';
        return <span>{name}</span>;
      }
      return (
        <div style={{ display: 'flex', flexWrap: 'wrap', gap: 8, justifyContent: 'center' }}>
          {ids.map(id => {
            const isAccepted = rowData?.driver && id === rowData.driver;
            const first = driverNameById?.[id]
              || (isAccepted && rowData?.driver_name ? rowData.driver_name.trim().split(/\s+/)[0] : (id || '').slice(0,6));
            return (
              <span
                key={id}
                style={{ color: isAccepted ? colors.GREEN : colors.BLACK, fontWeight: isAccepted ? 'bold' : 'normal' }}
              >
                {first},
              </span>
            );
          })}
        </div>
      );
    }
  },
  { title: t('DriverAccepted'), field: 'driver_name'},
  { title: t("Trip Type"),field: "roundTrip",
    render: rowData => { 
      let displayTrip;
      
      displayTrip = rowData.roundTrip === "true" ? "Round Trip" : "One Way Trip";
      return (
        <div
          style={{  
            padding: 7,  
            margin: 'auto'
          }}
        >
          {t(displayTrip)}
        </div>
      );
    } }, 
  { title: t('booking_status_web'), field: 'status',
   render: rowData => 
  <div
  style={{backgroundColor:rowData.status === "CANCELLED"?colors.RED :rowData.status=== "COMPLETE"?colors.GREEN : colors.YELLOW, color:"white", padding:7, borderRadius:"15px", fontWeight:"bold", width:"150px", margin: 'auto' }}
  >{t(rowData.status)}</div>,  },
    { title: t('otp'), field: 'otp',
  },
  {
    title: t('trip_cost'), field: 'trip_cost',
    render: (rowData) =>
      rowData.trip_cost
        ? settings.swipe_symbol
          ? rowData.trip_cost + " " + settings.symbol
          : settings.symbol + " " + rowData.trip_cost
        : settings.swipe_symbol
          ? "0 " + settings.symbol
          : settings.symbol + " 0",
  },
];

export const BookingModalBody = (props) => {
  const { t, i18n } = useTranslation();
  const isRTL = i18n.dir();
  const { classes, handleChange, auth, profileData, instructionData, otherPerson, setOtherPerson } = props;
  return (
    <span>
      {auth.profile.usertype === 'customer' && !auth.profile.firstName ?
        <Grid item xs={12}>
          <TextField
            InputLabelProps={{ style: { fontFamily: FONT_FAMILY } }}
            variant="outlined"
            margin="normal"
            required={auth.profile.firstName ? false : true}
            fullWidth
            id="firstName"
            label={t('firstname')}
            name="firstName"
            autoComplete="firstName"
            onChange={handleChange}
            value={profileData.firstName}
            autoFocus
            className={isRTL === 'rtl' ? classes.inputRtl : classes.textField}
            style={{ direction: isRTL === 'rtl' ? 'rtl' : 'ltr' }}
          />
        </Grid>
        : null}
      {auth.profile.usertype === 'customer' && !auth.profile.lastName ?
        <Grid item xs={12}>
          <TextField
            InputLabelProps={{ style: { fontFamily: FONT_FAMILY } }}
            variant="outlined"
            margin="normal"
            required={auth.profile.lastName ? false : true}
            fullWidth
            id="lastName"
            label={t('lastname')}
            name="lastName"
            autoComplete="lastName"
            onChange={handleChange}
            value={profileData.lastName}
            className={isRTL === 'rtl' ? classes.inputRtl : classes.textField}
            style={{ direction: isRTL === 'rtl' ? 'rtl' : 'ltr' }}
          />
        </Grid>
        : null}
      {auth.profile.usertype === 'customer' && !auth.profile.email ?
        <Grid item xs={12}>
          <TextField
            InputLabelProps={{ style: { fontFamily: FONT_FAMILY } }}
            variant="outlined"
            margin="normal"
            required={auth.profile.email ? false : true}
            fullWidth
            id="email"
            label={t('email')}
            name="email"
            autoComplete="email"
            onChange={handleChange}
            value={profileData.email}
            className={isRTL === 'rtl' ? classes.inputRtl : classes.textField}
            style={{ direction: isRTL === 'rtl' ? 'rtl' : 'ltr' }}
          />
        </Grid>
        : null}
      <OtherPerson
        classes={classes}
        otherPerson={otherPerson}
        handleChange={handleChange}
        setOtherPerson={setOtherPerson}
        instructionData={instructionData}
      />
      <Typography component="h2" variant="h5" style={{ marginTop: 15, color: colors.BLACK, fontFamily: FONT_FAMILY }}>
        {t('estimate_fare_text')}
      </Typography>
    </span>
  )
}
 
export const validateBookingObj = (t, bookingObject, instructionData) => {
  delete bookingObject.driverEstimates;
  return { bookingObject };
}

export const PanicSettings = (props) => {
  const { t, i18n } = useTranslation();
  const isRTL = i18n.dir();
  const { classes, data, handleTextChange } = props;
  return (
    <span>
      <Typography component="h1" variant="h5" style={{ marginTop: '15px', textAlign: isRTL === 'rtl' ? 'right' : 'left', fontFamily: FONT_FAMILY }}>
        {t('panic_num')}
      </Typography>
      <TextField
        InputLabelProps={{ style: { fontFamily: FONT_FAMILY } }}
        variant="outlined"
        margin="normal"
        fullWidth
        id="panic"
        label={t('panic_num')}
        className={isRTL === "rtl" ? [classes.rootRtl_1, classes.right] : classes.textField}
        name="panic"
        autoComplete="panic"
        onChange={handleTextChange}
        value={data.panic}
      />
    </span>
  )
}

export const DispatchSettings = (props) => {
  const { t, i18n } = useTranslation();
  const isRTL = i18n.dir();
  const { autoDispatch, onChange } = props;
  return (
    <FormControlLabel
      style={{ flexDirection: isRTL === 'rtl' ? 'row-reverse' : 'row' }}
      control={
        <Switch
          checked={autoDispatch}
          onChange={onChange}
          name="autoDispatch"
          color="primary"
        />
      }
      label={<Typography style={{ fontFamily: FONT_FAMILY }}>{t('auto_dispatch')}</Typography>}
    />
  )
}

export const BookingImageSettings = (props) => {
  return null;
}

export const carTypeColumns = (t, isRTL, onClick) => [
  { title: t('name'), field: 'name', },
  {
    title: t('image'), field: 'image',
    initialEditValue: 'https://cdn.pixabay.com/photo/2012/04/15/22/09/car-35502__480.png',
    render: rowData => rowData.image ? <button onClick={() => { onClick(rowData) }}><img alt='CarImage' src={rowData.image} style={{ width: 50 }} /></button> : null
  },
  { title: t('base_fare'), field: 'base_fare', type: 'numeric', initialEditValue: 0 },
  { title: t('Comfort Fare'), field: 'comfort_fare', type: 'numeric', initialEditValue: 0 },
  { title: t('Limo'), field: 'limo' },
  { title: t('rate_per_unit_distance'), field: 'rate_per_unit_distance', type: 'numeric', initialEditValue: 0 },
  { title: t('rate_per_hour'), field: 'rate_per_hour', type: 'numeric', initialEditValue: 0 },
  { title: t('min_fare'), field: 'min_fare', type: 'numeric', initialEditValue: 0 },
  // { title: t('convenience_fee'), field: 'convenience_fees', type: 'numeric', initialEditValue: 0 },
  // {
  //   title: t('convenience_fee_type'),
  //   field: 'convenience_fee_type',
  //   lookup: { flat: t('flat'), percentage: t('percentage') },
  // },
  {
    title: t('fleet_admin_comission'), field: 'fleet_admin_fee', type: 'numeric',
    initialEditValue: 0
  },
  {
    title: "Van Fare", field: 'special_fare', type: 'numeric',
    initialEditValue: 0
  },
  { title: t('extra_info'), field: 'extra_info', },
  { title: t('position'), field: 'pos', type: 'numeric', defaultSort: 'asc' }
];

export const acceptBid = (selectedBooking, selectedBidder) => {
  return null;
}

export const BidModal = (props) => {
  return null
}

export const downloadCsv = (data, fileName) => {
  const finalFileName = fileName.endsWith(".csv") ? fileName : `${fileName}.csv`;
  const a = document.createElement("a");
  a.href = URL.createObjectURL(new Blob([data], { type: "text/csv" }));
  a.setAttribute("download", finalFileName);
  document.body.appendChild(a);
  a.click();
  document.body.removeChild(a);
}

export const DeliveryFlow = (props) => {
  return null
}
export const FareTypeColumn = (t, isRTL, onClick) => [
  { title: "Pincode", field: 'pincode1' },
  { title: "Pincode", field: 'pincode2' },
  { title: "Fare", field: 'fare', type: 'numeric', initialEditValue: 0 }
];

export const RatesTypeColumn = (t, isRTL, onClick) => [
  { title: "From", field: 'pincode1' },
  { title: "To", field: 'pincode2' },
  { title: "Fare", field: 'fare', type: 'numeric', initialEditValue: 0 }
];

export const driverColumns = (t, settings, colors, onRidesClick) => [
  { title: t("first_name"), field: "firstName", initialEditValue: "" },  
  {
    title: t("mobile"),
    field: "mobile",
    editable: "onAdd",
    render: (rowData) =>
      settings.AllowCriticalEditsAdmin ? rowData.mobile : t("hidden_demo"),
  },
  {
    title: t("email"),
    field: "email",
    editable: "onAdd",
    render: (rowData) =>
      settings.AllowCriticalEditsAdmin ? rowData.email : t("hidden_demo"),
  },
  {
    title: t("driver_active"),
    field: "driverActiveStatus",
    editable: "never",
    type: "boolean",
    render: (rowData) => (
      <div style={{ 
        backgroundColor: rowData.driverActiveStatus ? colors.GREEN : colors.RED,
        color: 'white',
        padding: '4px 8px',
        borderRadius: '4px',
        textAlign: 'center'
      }}>
        {rowData.driverActiveStatus ? t('Active') : t('Inactive')}
      </div>
    ),
  },
  {
    title: t("queue"),
    field: "queue",
    editable: "never",
    type: "boolean",
    render: (rowData) => (
      <div style={{ 
        backgroundColor: rowData.queue ? colors.YELLOW : colors.GREEN,
        color: 'white',
        padding: '4px 8px',
        borderRadius: '4px',
        textAlign: 'center'
      }}>
        {rowData.queue ? t('In Queue') : t('Available')}
      </div>
    ),
  },
  {
    title: t("rides"),
    field: "rides",
    editable: "never",
    render: (rowData) => {
      console.log("rowData", rowData);
      const rideCount = rowData.rideCount || 0;
      const isDisabled = rideCount === 0;
      return (
        <div style={{ display: 'flex', gap: 8, justifyContent: 'center' }}>
          <button
            onClick={() => !isDisabled && onRidesClick && onRidesClick(rowData, 'active')}
            disabled={isDisabled}
            style={{
              backgroundColor: isDisabled ? colors.GRAY : (rideCount > 0 ? colors.GREEN : colors.GRAY),
              color: 'white',
              border: 'none',
              borderRadius: '5px',
              padding: '6px 12px',
              fontWeight: 'bold',
              cursor: isDisabled ? 'not-allowed' : 'pointer',
              fontSize: '0.9rem',
              minWidth: '60px',
              opacity: isDisabled ? 0.6 : 1,
            }}
          >
            {rideCount} {t('rides')}
          </button>
          <button
            onClick={() => onRidesClick && onRidesClick(rowData, 'history')}
            style={{
              backgroundColor: colors.TAXIPRIMARY,
              color: 'white',
              border: 'none',
              borderRadius: '5px',
              padding: '6px 12px',
              fontWeight: 'bold',
              cursor: 'pointer',
              fontSize: '0.9rem'
            }}
          >
            {t('Ride History')}
          </button>
        </div>
      );
    },
  }
];

export const customerTableColumns = (t, settings, colors, onRidesClick) => [
  { title: t("first_name"), field: "firstName", initialEditValue: "" },
  {
    title: t("mobile"),
    field: "mobile",
    editable: "onAdd",
    render: (rowData) =>
      settings.AllowCriticalEditsAdmin ? rowData.mobile : t("hidden_demo"),
  },
  {
    title: t("email"),
    field: "email",
    editable: "onAdd",
    render: (rowData) =>
      settings.AllowCriticalEditsAdmin ? rowData.email : t("hidden_demo"),
  },
  {
    title: t("rides"),
    field: "rides",
    editable: "never",
    render: (rowData) => { 
      return (
        <div style={{ display: 'flex', gap: 8, justifyContent: 'center' }}>
          <button
            onClick={() => onRidesClick && onRidesClick(rowData, 'history')}
            style={{
              backgroundColor: colors.TAXIPRIMARY,
              color: 'white',
              border: 'none',
              borderRadius: '5px',
              padding: '6px 12px',
              fontWeight: 'bold',
              cursor: 'pointer',
              fontSize: '0.9rem'
            }}
          >
            {t('Ride History')}
          </button>
        </div>
      );
    },
  }
];

export const calendarBookingColumns = (t, settings, colors, onAssignClick, onReassignClick, onCancelClick, role, driverNameById, onInfoClick) => [
  {
    title: t("Booking Ref"),
    field: "reference",
    render: rowData => (
      <div style={{ fontWeight: 'bold', color: colors.TAXIPRIMARY }}>
        {rowData.reference}
      </div>
    ),
  },
  {
    title: t("Time"),
    field: "tripdate",
    defaultSort: 'asc',
    render: rowData => (
      <div style={{ fontWeight: '500' }}>
        {moment(rowData.tripdate).format('hh:mm A')}
      </div>
    ),
  },
  { title: t('Customer Name'), field: 'customer_name'},
  {
    title: t("From"),
    field: "pickupAddress",
    render: rowData => {
      let displayAddress = rowData.pickupAddress;
      let backgroundColor = 'transparent';
      
      if (rowData.pickupAddress.includes("O'Hare International Airport")) {
        displayAddress = "ORD";
        backgroundColor = colors.GREEN;
      } else if (rowData.pickupAddress.includes("Midway International Airport")) {
        displayAddress = "MDW";
        backgroundColor = "#6C63FF"; // Purple-blue color
      }
      
      return (
        <div style={{ 
          maxWidth: '200px', 
          overflow: 'hidden', 
          textOverflow: 'ellipsis',
          whiteSpace: 'nowrap',
          fontSize: '0.9rem',
          backgroundColor: backgroundColor,
          color: backgroundColor !== 'transparent' ? 'white' : 'inherit',
          padding: backgroundColor !== 'transparent' ? '4px 8px' : '0',
          borderRadius: backgroundColor !== 'transparent' ? '8px' : '0',
          fontWeight: backgroundColor !== 'transparent' ? 'bold' : 'normal',
          textAlign: backgroundColor !== 'transparent' ? 'center' : 'left',
        }}>
          {displayAddress}
        </div>
      );
    },
  },
  {
    title: t("To"),
    field: "dropAddress",
    render: rowData => {
      let displayAddress = rowData.dropAddress;
      let backgroundColor = 'transparent';
      
      if (rowData.dropAddress.includes("O'Hare International Airport")) {
        displayAddress = "ORD";
        backgroundColor = colors.GREEN;
      } else if (rowData.dropAddress.includes("Midway International Airport")) {
        displayAddress = "MDW";
        backgroundColor = "#6C63FF"; // Purple-blue color
      }
      
      return (
        <div style={{ 
          maxWidth: '200px', 
          overflow: 'hidden', 
          textOverflow: 'ellipsis',
          whiteSpace: 'nowrap',
          fontSize: '0.9rem',
          backgroundColor: backgroundColor,
          color: backgroundColor !== 'transparent' ? 'white' : 'inherit',
          padding: backgroundColor !== 'transparent' ? '4px 8px' : '0',
          borderRadius: backgroundColor !== 'transparent' ? '8px' : '0',
          fontWeight: backgroundColor !== 'transparent' ? 'bold' : 'normal',
          textAlign: backgroundColor !== 'transparent' ? 'center' : 'left',
        }}>
          {displayAddress}
        </div>
      );
    },
  },
  { 
    title: t('Assigned_by'), 
    field: 'customerName',
    render: rowData => {
      let assignedBy = '';
      
      if (rowData.booking_type_admin === true) {
        assignedBy = t('Admin');
      } else if (rowData.booking_type_admin === false && rowData.fleetadmin) {
        assignedBy = t('Fleet Admin');
      } else if (rowData.booking_type_admin === false && !rowData.fleetadmin) {
        assignedBy = t('Customer');
      } else {
        // Fallback to customer name if available
        assignedBy = rowData.customer_name || t('Customer');
      }
      
      return (
        <div style={{ 
          fontWeight: '500',
          color: colors.BLACK,
          fontSize: '0.9rem'
        }}>
          {assignedBy}
        </div>
      );
    }
  },
  {
    title: t('AdminAssigned'),
    field: 'driver_name',
    render: rowData => {
      const ids = rowData?.requestedDrivers ? Object.keys(rowData.requestedDrivers) : [];
      if (!ids.length) {
        const name = rowData?.driver_name ? rowData.driver_name.trim().split(/\s+/)[0] : '';
        return <span>{name}</span>;
      }
      return (
        <div style={{ display: 'flex', flexWrap: 'wrap', gap: 8, justifyContent: 'center' }}>
          {ids.map(id => {
            const isAccepted = rowData?.driver && id === rowData.driver;
            const first = driverNameById?.[id]
              || (isAccepted && rowData?.driver_name ? rowData.driver_name.trim().split(/\s+/)[0] : (id || '').slice(0,6));
            return (
              <span
                key={id}
                style={{ color: isAccepted ? colors.GREEN : colors.BLACK, fontWeight: isAccepted ? 'bold' : 'normal' }}
              >
                {first},
              </span>
            );
          })}
        </div>
      );
    },
  },
  {
    title: t("Driver"),
    field: "driver_name",
    render: rowData => (
      <div style={{ fontWeight: '500', color: rowData.driver_name ? colors.BLACK : colors.GRAY }}>
        {rowData.driver_name || t("Not Assigned")}
      </div>
    ),
  },
  {
    title: t("Status"),
    field: "status",
    render: rowData => (
      <div style={{
        backgroundColor: 
          rowData.status === 'ACCEPTED' ? colors.GREEN :
          rowData.status === 'CANCELLED' ? colors.RED :
          rowData.status === 'COMPLETE' ? colors.BLUE :
          rowData.status === 'NEW' ? colors.YELLOW :
          colors.GRAY,
        color: 'white',
        padding: '4px 8px',
        borderRadius: '12px',
        fontSize: '0.75rem',
        fontWeight: 'bold',
        textTransform: 'uppercase',
        textAlign: 'center',
        minWidth: '80px'
      }}>
        {t(rowData.status)}
      </div>
    ),
  },
  {
    title: t("Car Type"),
    field: "carType",
    render: rowData => (
      <div style={{ 
        color: rowData.carType ? colors.BLACK : colors.GRAY,
        fontSize: '0.9rem'
      }}>
        {rowData.carType || t("Not Specified")}
      </div>
    ),
  },
  {
    title: t("Trip Cost"),
    field: "trip_cost",
    render: rowData => (
      <div style={{ 
        fontWeight: 'bold',
        color: colors.TAXIPRIMARY,
        fontSize: '0.9rem'
      }}>
        {settings.swipe_symbol
          ? rowData.trip_cost + " " + settings.symbol
          : settings.symbol + " " + rowData.trip_cost}
      </div>
    ),
  },
  {
    title: t("Actions"),
    field: "actions",
    render: rowData => {
      const hasDriver = rowData.driver_name && rowData.driver;
      const canAssign = (role === 'admin' || role === 'fleetadmin') && !hasDriver && rowData.status === 'NEW';
      const canReassign = (role === 'admin' || role === 'fleetadmin') && hasDriver && (rowData.status === 'NEW' || rowData.status === 'ACCEPTED');
      const canCancel = (role === 'admin' || role === 'fleetadmin') && rowData.status !== 'CANCELLED' && rowData.status !== 'COMPLETE';
      
      return (
        <div style={{ display: 'flex', gap: '8px', justifyContent: 'center', flexWrap: 'wrap' }}>
          {/* {onInfoClick && (
            <button
              onClick={() => onInfoClick(rowData)}
              style={{
                backgroundColor: colors.TAXIPRIMARY,
                color: 'white',
                border: 'none',
                borderRadius: '50%',
                padding: '6px',
                fontSize: '0.8rem',
                fontWeight: 'bold',
                cursor: 'pointer',
                minWidth: '20px',
                display: 'flex',
                alignItems: 'center',
                gap: '4px',
              }}
            >
              <InfoIcon style={{ fontSize: '16px' }} />
            </button>
          )} */}
          {canAssign && (
            <button
              onClick={() => onAssignClick && onAssignClick(rowData)}
              style={{
                backgroundColor: colors.GREEN,
                color: 'white',
                border: 'none',
                borderRadius: '6px',
                padding: '6px 12px',
                fontSize: '0.8rem',
                fontWeight: 'bold',
                cursor: 'pointer',
                minWidth: '70px',
              }}
            >
              {t("Assign")}
            </button>
          )}
          {canReassign && (
            <button
              onClick={() => onReassignClick && onReassignClick(rowData)}
              style={{
                backgroundColor: colors.YELLOW,
                color: 'black',
                border: 'none',
                borderRadius: '6px',
                padding: '6px 12px',
                fontSize: '0.8rem',
                fontWeight: 'bold',
                cursor: 'pointer',
                minWidth: '70px',
              }}
            >
              {t("Reassign")}
            </button>
          )}
          {canCancel && (
            <button
              onClick={() => onCancelClick && onCancelClick(rowData)}
              style={{
                backgroundColor: colors.RED,
                color: 'white',
                border: 'none',
                borderRadius: '6px',
                padding: '6px 12px',
                fontSize: '0.8rem',
                fontWeight: 'bold',
                cursor: 'pointer',
                minWidth: '70px',
              }}
            >
              {t("Cancel")}
            </button>
          )}
        </div>
      );
    },
  }
];
